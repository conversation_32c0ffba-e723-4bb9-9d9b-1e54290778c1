// Import Bootstrap-like styles for the copy trading page
.copy-trading {
    background-color: #121212;
    color: #ffffff;
    font-size: 0.85rem;
    font-weight: 400;
    width: 100%;
    height: 100vh;
    min-height: 100vh;
    padding: 1rem;
    overflow-y: auto;
    box-sizing: border-box;
    position: relative;

    .card {
        background-color: #1f1f1f;
        border: none;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.5);
        margin-bottom: 20px;
        font-size: 0.85rem;
        font-weight: 400;
        padding: 1rem;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.75rem;
        font-weight: 400;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.3s ease;
    }

    .btn-green {
        background-color: #28a745;
        color: white;

        &:hover {
            background-color: #218838;
        }
    }

    .btn-blue {
        background-color: #007bff;
        color: white;
    }

    .btn-cyan {
        background-color: #00e5ff;
        color: black;

        &:hover {
            background-color: #00bcd4;
        }
    }

    .replicator-token {
        background-color: #1a3b73;
        color: #fff;
        font-weight: 400;
        padding: 15px 20px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .form-control {
        background-color: #2b2b2b;
        color: white;
        border: 1px solid #444;
        font-size: 0.85rem;
        font-weight: 400;
        padding: 0.5rem;
        border-radius: 4px;
        width: 100%;
        box-sizing: border-box;
        min-height: 40px;
    }

    h5, h6 {
        color: #e0e0e0;
        font-size: 1rem;
        font-weight: 400;
        margin-bottom: 1rem;
    }

    .text-muted {
        color: #aaa !important;
        font-size: 0.75rem;
        font-weight: 400;
    }

    .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        gap: 0.75rem;
    }

    // Remove YouTube icon and ensure no gap remains
    .youtube-icon { display: none !important; }

    .status-msg {
        color: green;
        opacity: 0;
        transform: translateY(-10px);
        transition: opacity 0.3s ease, transform 0.3s ease;
        margin: 4px 0 0;
        display: block;
        font-size: 0.75rem;
        font-weight: 700;

        &.show {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .input-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .form-control {
            flex: 1;
        }
    }

    .d-flex {
        display: flex;

        &.gap-2 {
            gap: 0.5rem;
        }
    }

    .mb-2 {
        margin-bottom: 0.5rem;
    }

    .mb-3 {
        margin-bottom: 1rem;
    }

    .p-3 {
        padding: 1rem;
    }

    #tokenTable {
        color: white;
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;

        th, td {
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid #444;
        }

        th {
            background-color: #2b2b2b;
            font-weight: 600;
        }

        .delete-btn {
            color: red;
            cursor: pointer;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;

            &:hover {
                background-color: rgba(255, 0, 0, 0.1);
            }
        }
    }

    // Responsive Design Breakpoints

    // Large Desktop (1200px and up)
    @media (min-width: 1200px) {
        padding: 2rem;
        font-size: 1rem;

        .card {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .top-bar {
            margin-bottom: 2rem;
        }

        .replicator-token {
            padding: 20px 25px;
            font-size: 1rem;
        }

        h5, h6 {
            font-size: 1.2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
        }

        #tokenTable {
            th, td {
                padding: 0.75rem;
            }
        }
    }

    // Desktop/Laptop (992px to 1199px)
    @media (min-width: 992px) and (max-width: 1199px) {
        padding: 1.5rem;
        font-size: 0.9rem;

        .card {
            padding: 1.25rem;
        }

        .replicator-token {
            padding: 18px 22px;
        }

        .btn {
            padding: 0.6rem 1.2rem;
            font-size: 0.8rem;
        }
    }

    // Tablet (768px to 991px)
    @media (min-width: 768px) and (max-width: 991px) {
        padding: 1rem;
        font-size: 0.85rem;

        .top-bar {
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            text-align: center;
        }

        .card {
            padding: 1rem;
        }

        .input-group {
            flex-direction: column;

            .form-control {
                margin-bottom: 0.5rem;
            }
        }

        .d-flex.gap-2 {
            flex-wrap: wrap;
            justify-content: center;
        }

        .replicator-token {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
            padding: 15px 20px;
        }

        #tokenTable {
            font-size: 0.8rem;

            th, td {
                padding: 0.5rem 0.25rem;
            }
        }
    }

    // Mobile Large (576px to 767px)
    @media (min-width: 576px) and (max-width: 767px) {
        padding: 0.75rem;
        font-size: 0.8rem;

        .top-bar {
            flex-direction: column;
            gap: 0.75rem;
            align-items: center;
        }

        .card {
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .input-group {
            flex-direction: column;

            .form-control {
                margin-bottom: 0.5rem;
                min-height: 45px;
            }
        }

        .d-flex.gap-2 {
            flex-direction: column;
            gap: 0.5rem;
        }

        .replicator-token {
            flex-direction: column;
            gap: 0.75rem;
            text-align: center;
            padding: 12px 15px;
        }

        h5, h6 {
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        #tokenTable {
            font-size: 0.75rem;

            th, td {
                padding: 0.4rem 0.2rem;
                word-break: break-all;
            }

            th:first-child,
            td:first-child {
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .youtube-icon {
            img {
                width: 28px;
                height: 28px;
            }

            div {
                font-size: 0.6rem;
            }
        }
    }

    // Mobile Small (up to 575px)
    @media (max-width: 575px) {
        padding: 0.5rem;
        font-size: 0.75rem;
        height: 100vh;
        overflow-y: auto;

        .top-bar {
            flex-direction: column;
            gap: 0.5rem;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .card {
            padding: 0.5rem;
            margin-bottom: 0.75rem;
            border-radius: 6px;
        }

        .btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.7rem;
            width: 100%;
            margin-bottom: 0.4rem;
            border-radius: 6px;
        }

        .input-group {
            flex-direction: column;
            margin-bottom: 0.5rem;

            .form-control {
                margin-bottom: 0.4rem;
                min-height: 40px;
                font-size: 0.75rem;
                padding: 0.4rem;
            }
        }

        .d-flex.gap-2 {
            flex-direction: column;
            gap: 0.4rem;
        }

        .replicator-token {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
            padding: 10px 12px;
            font-size: 0.75rem;

            h5 {
                font-size: 0.8rem;
                margin: 0;
            }
        }

        h5, h6 {
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .text-muted {
            font-size: 0.65rem;
        }

        #tokenTable {
            font-size: 0.65rem;

            th, td {
                padding: 0.3rem 0.1rem;
                word-break: break-all;
            }

            th:first-child,
            td:first-child {
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .delete-btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }
        }

        .youtube-icon {
            img {
                width: 24px;
                height: 24px;
            }

            div {
                font-size: 0.55rem;
            }
        }

        .status-msg {
            font-size: 0.65rem;
            margin: 2px 0 0;
        }
    }

    // Extra responsive utilities
    .mb-2 {
        @media (max-width: 575px) {
            margin-bottom: 0.4rem;
        }
    }

    .mb-3 {
        @media (max-width: 575px) {
            margin-bottom: 0.6rem;
        }
    }

    .p-3 {
        @media (max-width: 575px) {
            padding: 0.5rem;
        }
    }
}
