<xml xmlns="http://www.w3.org/1999/xhtml" collection="false">
  <variables>
    <variable type="" id="o!-=j_eJZCfW(+iV7;MS">Tick 1</variable>
    <variable type="" id="7Q4y$nr_sr!x2NkOu%)2">Stake</variable>
    <variable type="" id="b~M_gc!6d^tXHe/]?+}`">Tick 2</variable>
    <variable type="" id=":Z8WvPXWG?qCe|8=iii1">Expected Profit</variable>
    <variable type="" id="S10~wx4EJ/w3gZZ;v77Y">Total Lost</variable>
    <variable type="" id="!}o95im:dfosc$6[v%LW">Tick 3</variable>
    <variable type="" id="~ZEk9Zr7t[g;-`afIGOO">Initial Stake</variable>
    <variable type="" id="L.cN$B-UUzkS|eDQm2xZ">Stop Loss</variable>
    <variable type="" id="Op-Cim@t?DJN?i;G)w)C">Count Loss</variable>
    <variable type="" id="S|AWDr;xbKU_pbl(^]^B">Tick 4</variable>
    <variable type="" id="!mQjsA[]viO$7Gu~UzUn">Martingale Split</variable>
    <variable type="" id="VK7:nSRSXJ=|#p(oAU9v">Payout %</variable>
  </variables>
  <block type="tick_analysis" id="dtqb:oG+omHwo.Q|Fn;v" collapsed="true" x="453" y="-675">
    <statement name="TICKANALYSIS_STACK">
      <block type="notify" id="EW:q+aMegzKrs}|4J,E7">
        <field name="NOTIFICATION_TYPE">success</field>
        <field name="NOTIFICATION_SOUND">silent</field>
        <value name="MESSAGE">
          <shadow type="text" id="%SIm)}T(nDOv$q4Oh6Rg">
            <field name="TEXT">Official website &gt;360tradinghub.co.ke &lt;</field>
          </shadow>
        </value>
        <next>
          <block type="variables_set" id="pvc,YIdZ,IPf+Hf3Y;,`">
            <field name="VAR" id="o!-=j_eJZCfW(+iV7;MS" variabletype="">Tick 1</field>
            <value name="VALUE">
              <block type="lists_getIndex" id="UFfh]6*q:b-/^WrKgPHq">
                <mutation statement="false" at="true"></mutation>
                <field name="MODE">GET</field>
                <field name="WHERE">FROM_END</field>
                <value name="VALUE">
                  <block type="lastDigitList" id="-8YnnCg|RXH!S(]$#hw^"></block>
                </value>
                <value name="AT">
                  <block type="math_number" id="~W}|kTWKTyj0/Evj,,Ep">
                    <field name="NUM">1</field>
                  </block>
                </value>
              </block>
            </value>
            <next>
              <block type="variables_set" id="-7e/$:;j*mUx1[.#/XzY">
                <field name="VAR" id="b~M_gc!6d^tXHe/]?+}`" variabletype="">Tick 2</field>
                <value name="VALUE">
                  <block type="lists_getIndex" id="32|e+-F{(XDra{D~$*@G">
                    <mutation statement="false" at="true"></mutation>
                    <field name="MODE">GET</field>
                    <field name="WHERE">FROM_END</field>
                    <value name="VALUE">
                      <block type="lastDigitList" id="av/P$$tE)CL=2PO![AR."></block>
                    </value>
                    <value name="AT">
                      <block type="math_number" id="CX/ZpdHX;{LZPL)!ApB?">
                        <field name="NUM">2</field>
                      </block>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="mB]91s22+#_JqUO`BN4w">
                    <field name="VAR" id="!}o95im:dfosc$6[v%LW" variabletype="">Tick 3</field>
                    <value name="VALUE">
                      <block type="lists_getIndex" id="_Yf,VkEN+)Wr1vDdzPAl">
                        <mutation statement="false" at="true"></mutation>
                        <field name="MODE">GET</field>
                        <field name="WHERE">FROM_END</field>
                        <value name="VALUE">
                          <block type="lastDigitList" id="lh}#?HG}xj}ne/CAWsGc"></block>
                        </value>
                        <value name="AT">
                          <block type="math_number" id="3o*:9-+Y]x@]Ll5IApA$">
                            <field name="NUM">3</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="8%*h`5Bqeh?qI|nWSGY*">
                        <field name="VAR" id="S|AWDr;xbKU_pbl(^]^B" variabletype="">Tick 4</field>
                        <value name="VALUE">
                          <block type="lists_getIndex" id="8/h$ZK;mjxktZ;$qW*JD">
                            <mutation statement="false" at="true"></mutation>
                            <field name="MODE">GET</field>
                            <field name="WHERE">FROM_END</field>
                            <value name="VALUE">
                              <block type="lastDigitList" id=":6$i(+gPvAL}kh~%/wyC"></block>
                            </value>
                            <value name="AT">
                              <block type="math_number" id=":~%Y!cSnG/O?q1#kt7bg">
                                <field name="NUM">4</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <next>
                          <block type="notify" id="U3;j/?Y5xH}MlQ9)_c|W">
                            <field name="NOTIFICATION_TYPE">error</field>
                            <field name="NOTIFICATION_SOUND">silent</field>
                            <value name="MESSAGE">
                              <shadow type="text" id="%SIm)}T(nDOv$q4Oh6Rg">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="text_join" id="%zf(q=t0jXjBGGa!ouKF">
                                <mutation items="3"></mutation>
                                <value name="ADD0">
                                  <block type="text" id="O^Ct|*L_,.B`c]xo$bd;">
                                    <field name="TEXT"> Last Appearing Digit&gt;  | </field>
                                  </block>
                                </value>
                                <value name="ADD1">
                                  <block type="text" id="w^P8t(,.D!@S6F;tYig3" collapsed="true">
                                    <field name="TEXT">  </field>
                                  </block>
                                </value>
                                <value name="ADD2">
                                  <block type="variables_get" id="(F6X.OT#GABRv.C^z+MA" collapsed="true">
                                    <field name="VAR" id="o!-=j_eJZCfW(+iV7;MS" variabletype="">Tick 1</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="trade" id="xgH69|xFn9=70w.*3Vo@" x="0" y="0">
    <field name="MARKET_LIST">synthetic_index</field>
    <field name="SUBMARKET_LIST">random_index</field>
    <field name="SYMBOL_LIST">R_100</field>
    <field name="TRADETYPECAT_LIST">digits</field>
    <field name="TRADETYPE_LIST">overunder</field>
    <field name="TYPE_LIST">DIGITUNDER</field>
    <field name="CANDLEINTERVAL_LIST">60</field>
    <field name="TIME_MACHINE_ENABLED">FALSE</field>
    <field name="RESTARTONERROR">TRUE</field>
    <statement name="INITIALIZATION">
      <block type="text_print" id="=y^Og{NJ@:BxwwGQCCGR" collapsed="true">
        <value name="TEXT">
          <shadow type="text" id="s??4tVv$b{Q-X%eN5@RW">
            <field name="TEXT">360 Printer Bot &gt;  Version 2.2 &gt; www.360tradinghub.co.ke&gt;&gt;Whatsap +254748998726  &lt;  &gt;  [ Get this bot's strategy from Admin/ Developer to stay safe ] /</field>
          </shadow>
        </value>
        <next>
          <block type="variables_set" id="25;UKm=tH:vV0Ry:S?E#">
            <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
            <value name="VALUE">
              <block type="math_number" id="kP,5$ysG=+vyr*mmE]Bd">
                <field name="NUM">1</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="g}W~QV[`Uw(!DCjz7}0G">
                <field name="VAR" id=":Z8WvPXWG?qCe|8=iii1" variabletype="">Expected Profit</field>
                <value name="VALUE">
                  <block type="math_number" id="zT]b;C]z)?Hrp$oW|PVF">
                    <field name="NUM">20</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="[Ng$e=!6(AlGP{EaF-U=" collapsed="true">
                    <field name="VAR" id="~ZEk9Zr7t[g;-`afIGOO" variabletype="">Initial Stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="KZ.[|+t[-F6evufk%U0Q">
                        <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="$gb]TAg,ll7JP}IVJ:,H">
                        <field name="VAR" id="!mQjsA[]viO$7Gu~UzUn" variabletype="">Martingale Split</field>
                        <value name="VALUE">
                          <block type="math_number" id="0ZhXDxhU.6^CCG^|@yY5">
                            <field name="NUM">2</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="aJ`F50SxB5_[Jq,a|Ul%">
                            <field name="VAR" id="VK7:nSRSXJ=|#p(oAU9v" variabletype="">Payout %</field>
                            <value name="VALUE">
                              <block type="math_number" id="1}.fD$$^BG9nIjSANzi}">
                                <field name="NUM">39</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="CunuO[sj?:hAlJ3k};|i">
                                <field name="VAR" id="L.cN$B-UUzkS|eDQm2xZ" variabletype="">Stop Loss</field>
                                <value name="VALUE">
                                  <block type="math_number" id="c,yl^vC$[?!N)e?|4_y@">
                                    <field name="NUM">1000</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="tradeOptions" id="x=V33~4Lb|(sLv`J[:Eb">
        <field name="DURATIONTYPE_LIST">t</field>
        <field name="BARRIEROFFSETTYPE_LIST">+</field>
        <field name="SECONDBARRIEROFFSETTYPE_LIST">-</field>
        <value name="DURATION">
          <shadow type="math_number" id="pG}3}OC=R[!x#GxvU=VP">
            <field name="NUM">1</field>
          </shadow>
        </value>
        <value name="AMOUNT">
          <shadow type="math_number" id="ml)25~7^q}3I9}vjf:%K">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="s)Izwt}2H18zS`E_2~Mo">
            <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number" id="FJsXw7GMZD|sb!/a@Gzn">
            <field name="NUM">7</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="i-CIx.(Onm4?ihxzA}Y]" x="1" y="610">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="controls_if" id="YRxKDZ`rJAWkxc3S;miM" collapsed="true">
        <value name="IF0">
          <block type="check_direction" id="K8rb5?25:YNh?dkDnk^|" collapsed="true">
            <field name="CHECK_DIRECTION">rise</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id=";fv4tvdu@mys#xWO:2cp">
            <value name="IF0">
              <block type="logic_compare" id="Fd~|0Mrx$0+)Tp9|#Qr{">
                <field name="OP">GTE</field>
                <value name="A">
                  <block type="variables_get" id="=UH6Len317cF($V_[mvf">
                    <field name="VAR" id="o!-=j_eJZCfW(+iV7;MS" variabletype="">Tick 1</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="3VRo1%Gsq88cDIAC57#,">
                    <field name="NUM">3</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="I8E:#-Y.%W/|aNsiyW`Q">
                <value name="IF0">
                  <block type="logic_compare" id="/+Eo#z##*zU$}fK4zjr%">
                    <field name="OP">NEQ</field>
                    <value name="A">
                      <block type="variables_get" id="@g#ux_S]FGZ;fpWUf?fs">
                        <field name="VAR" id="o!-=j_eJZCfW(+iV7;MS" variabletype="">Tick 1</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="rML[,GG=0(di9tIpaPoP">
                        <field name="NUM">7</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id=")1|VXqN^raR+9D!aDZl)">
                    <value name="IF0">
                      <block type="logic_compare" id="3~-6jqfZROk$M1%=/::9">
                        <field name="OP">NEQ</field>
                        <value name="A">
                          <block type="variables_get" id="(MO7:Oc;!eA1/.6uRrNn">
                            <field name="VAR" id="o!-=j_eJZCfW(+iV7;MS" variabletype="">Tick 1</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="jOBh0F!{chVk~Adx.zr5">
                            <field name="NUM">8</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="notify" id="_gc*83Y!eXmr0N9|BHc8">
                        <field name="NOTIFICATION_TYPE">warn</field>
                        <field name="NOTIFICATION_SOUND">silent</field>
                        <value name="MESSAGE">
                          <shadow type="text" id="gY1otfP6ZIQNO)n!ph|V">
                            <field name="TEXT">|| Trade Signal Found || </field>
                          </shadow>
                        </value>
                        <next>
                          <block type="purchase" id="W6q#Z-I3q3d[d3-`S[dS">
                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="D^Jz1^n=2vtZku1vBN@;" collapsed="true" x="2401" y="727">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="~Mc%BQ];gCuZ6liBCCfG">
        <mutation elseif="1" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="d7esPE!SLbPHmQ75~Trr">
            <field name="OP">GT</field>
            <value name="A">
              <block type="total_profit" id="87[H}!^.o0%nXSKYy3MB"></block>
            </value>
            <value name="B">
              <block type="variables_get" id="+DPK1w}6DYOllDJx@U%N">
                <field name="VAR" id=":Z8WvPXWG?qCe|8=iii1" variabletype="">Expected Profit</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_print" id="SdPlYWBVrQU;mRVv?^yS">
            <value name="TEXT">
              <shadow type="text" id="@:Jnmzj5kdz$HX%o9ai(">
                <field name="TEXT">abc</field>
              </shadow>
              <block type="text_join" id="}$~O5e?LkaT_W*`$t!PC" collapsed="true">
                <mutation items="3"></mutation>
                <value name="ADD0">
                  <block type="text" id="r2jRJ=WGe)eDGm7:E87h">
                    <field name="TEXT">360 Printer Bot Version 2.1 </field>
                  </block>
                </value>
                <value name="ADD1">
                  <block type="text" id="p_v+.5EttB`VbZPckUUF">
                    <field name="TEXT">&lt;&lt; CONGRATULATIONS. &gt;&gt; You have successfully printed&gt;  &amp;</field>
                  </block>
                </value>
                <value name="ADD2">
                  <block type="total_profit" id="cr~arG`)`^lS5Uz5o+r@"></block>
                </value>
              </block>
            </value>
          </block>
        </statement>
        <value name="IF1">
          <block type="logic_compare" id="*tfm-k}{0*u[jlZ:i%hc">
            <field name="OP">LTE</field>
            <value name="A">
              <block type="total_profit" id=")2Eo=j[RwkGC793b@ug9"></block>
            </value>
            <value name="B">
              <block type="math_single" id="!SXf?5B4wUV1Isz-jVgh">
                <field name="OP">NEG</field>
                <value name="NUM">
                  <shadow type="math_number" id="4*b+:0qU}oAIDw2TD~57">
                    <field name="NUM">9</field>
                  </shadow>
                  <block type="variables_get" id="Z`|DxW@L^7BjA[^/2CsT">
                    <field name="VAR" id="L.cN$B-UUzkS|eDQm2xZ" variabletype="">Stop Loss</field>
                  </block>
                </value>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO1">
          <block type="text_print" id="umH7O(wWOj;Wz9e4].I:" collapsed="true">
            <value name="TEXT">
              <shadow type="text" id="bb6#%zzx*`x|aE%0:L3$">
                <field name="TEXT">Ooops! You hit your  Stop Loss . Get Strategy from 360 Admin&gt; 0748 998 726&gt; </field>
              </shadow>
            </value>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="controls_if" id="W89yMZ#z,$ovE8JiG4gs">
            <mutation else="1"></mutation>
            <value name="IF0">
              <block type="contract_check_result" id="SDWQJZ0A+S7jI=BQN??@">
                <field name="CHECK_RESULT">loss</field>
              </block>
            </value>
            <statement name="DO0">
              <block type="math_change" id="B1/8j0_2BFjf3t(edCkg">
                <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                <value name="DELTA">
                  <shadow type="math_number" id="FP*Ah$#tSM5sH`|Js/P^">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="variables_get" id="[;2#tO*,]Dx?UVi2KrV[">
                    <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="gU(-!+/LaZ+2)cg+1m8p" collapsed="true">
                    <value name="IF0">
                      <block type="logic_compare" id="u%#tG[)eV.B=xLBYzV;z">
                        <field name="OP">GT</field>
                        <value name="A">
                          <block type="variables_get" id="R%zbW*L[jQXDK+5e^4HW">
                            <field name="VAR" id="Op-Cim@t?DJN?i;G)w)C" variabletype="">Count Loss</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="1N/iA8GDf/y-@KKe|cPS">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="GH%QkME7#t,4_p(2A0[Z">
                        <field name="VAR" id="Op-Cim@t?DJN?i;G)w)C" variabletype="">Count Loss</field>
                        <value name="VALUE">
                          <block type="math_number" id="i*lTaH|-e,Q]UEDh$iWX">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </statement>
            <statement name="ELSE">
              <block type="math_change" id="./K[E^j46b.3z14#ye6$" collapsed="true">
                <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                <value name="DELTA">
                  <shadow type="math_number" id="Y5cd{u14F#qFuY1J:Rb=">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="math_single" id="=W35I8*FQQWH4IYbC9#B">
                    <field name="OP">NEG</field>
                    <value name="NUM">
                      <shadow type="math_number" id="ET73.lmAPzCY3G,ni8NI">
                        <field name="NUM">9</field>
                      </shadow>
                      <block type="read_details" id="OOG}4I/WHY]Pp;~6aIaw">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="XP6=|[0qD#I)jI;?F:dX">
                    <value name="IF0">
                      <block type="logic_compare" id="mUKAAgO;Rbo*1%R-$JS(">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="variables_get" id="1p_?)eY=IU~V$8;2wWYs">
                            <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="!R~F4H})4%^X)`)Iy6xi">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="+z$J~7gKgu#PMPm[!_Ua">
                        <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                        <value name="VALUE">
                          <block type="math_number" id="BvtGDSLY~2nF|AM7k4FI">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="controls_if" id="6MqXgtcizzmX@%V2(y[f" collapsed="true">
                <mutation else="1"></mutation>
                <value name="IF0">
                  <block type="logic_compare" id="2=yz49R.(Bu78ll!vWMd">
                    <field name="OP">GT</field>
                    <value name="A">
                      <block type="variables_get" id="(*T-qJKnF7_;,Tl0qd1A">
                        <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="(IVEBDakT-^C0q0Arx9~">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="math_change" id="n]D/C`)?|=+6Zlb(OrA-">
                    <field name="VAR" id="Op-Cim@t?DJN?i;G)w)C" variabletype="">Count Loss</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="L_)E7kSmhLREA]xMrmzB">
                        <field name="NUM">1</field>
                      </shadow>
                    </value>
                    <next>
                      <block type="controls_if" id="l5VoVIT`]XQ/$0E%z9`J">
                        <value name="IF0">
                          <block type="logic_compare" id="=O9CuApoQ:(3(XF6l5QZ">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="variables_get" id="=?bh-c?1$_rF=?t`)-44">
                                <field name="VAR" id="Op-Cim@t?DJN?i;G)w)C" variabletype="">Count Loss</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="=*ak)9dQwDoU/G*9~1BV">
                                <field name="NUM">1</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="*t%Uu#Inxkkl-5;4;~W`">
                            <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                            <value name="VALUE">
                              <block type="math_arithmetic" id="d0HkJ,,bEWl4M*34dVZS">
                                <field name="OP">DIVIDE</field>
                                <value name="A">
                                  <shadow type="math_number" id="^:xXGkPTD6xJ(yjyhstC">
                                    <field name="NUM">1</field>
                                  </shadow>
                                  <block type="math_arithmetic" id="D$cm|Z]:5B)wwc*bNe~M">
                                    <field name="OP">MULTIPLY</field>
                                    <value name="A">
                                      <shadow type="math_number" id="pukEi.M_JTDT:0W=`l*4">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="variables_get" id="0o;EEKE%q*EV;}OR.{.2">
                                        <field name="VAR" id="S10~wx4EJ/w3gZZ;v77Y" variabletype="">Total Lost</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <shadow type="math_number" id="+q3~82C@i?%VXQr@n`Nc">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="math_arithmetic" id="F43?puVE3lIf{j)9ry@y">
                                        <field name="OP">DIVIDE</field>
                                        <value name="A">
                                          <shadow type="math_number" id="k^ql`gG]4*[ko_kA@TY4">
                                            <field name="NUM">100</field>
                                          </shadow>
                                        </value>
                                        <value name="B">
                                          <shadow type="math_number" id="4I#nUbSw5.e@c-vWOEA}">
                                            <field name="NUM">24</field>
                                          </shadow>
                                          <block type="variables_get" id="(-Pu8+m|~v6o/|9bcho6">
                                            <field name="VAR" id="VK7:nSRSXJ=|#p(oAU9v" variabletype="">Payout %</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <value name="B">
                                  <shadow type="math_number" id="_%v2+E|;XNl!:$Gef/VY">
                                    <field name="NUM">1</field>
                                  </shadow>
                                  <block type="variables_get" id="U|f?p-`U*sR=qTgE9Wbq">
                                    <field name="VAR" id="!mQjsA[]viO$7Gu~UzUn" variabletype="">Martingale Split</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="variables_set" id=".d7YDrF|A}2SdvWBYA_h">
                    <field name="VAR" id="Op-Cim@t?DJN?i;G)w)C" variabletype="">Count Loss</field>
                    <value name="VALUE">
                      <block type="math_number" id="F)=~_[$L.*B;hB_)K**5">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="f?H*.b@+EV3E1B*8)k_(">
                        <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                        <value name="VALUE">
                          <block type="variables_get" id="uhS]Y=OWO#dLNxU-I4~Q">
                            <field name="VAR" id="~ZEk9Zr7t[g;-`afIGOO" variabletype="">Initial Stake</field>
                          </block>
                        </value>
                      </block>
                    </next>
                  </block>
                </statement>
                <next>
                  <block type="controls_if" id="6pr2:Wm^:)zg]Wb-p,0j" collapsed="true">
                    <value name="IF0">
                      <block type="logic_compare" id="Fc^G0ktdb60Bxr#vzBBe">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="variables_get" id="?xv4Pg4v*Q9`b^7ylQv+">
                            <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="{V|KzoP[*V=z*,)n5Yh}">
                            <field name="NUM">0.35</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="}ko=9B]XW}@gOG!@`}_3">
                        <field name="VAR" id="7Q4y$nr_sr!x2NkOu%)2" variabletype="">Stake</field>
                        <value name="VALUE">
                          <block type="math_number" id="{:rKo.SozhFUh!g7#,2_">
                            <field name="NUM">0.35</field>
                          </block>
                        </value>
                      </block>
                    </statement>
                    <next>
                      <block type="trade_again" id="c)S|KjoQdXc8l#J@5z3P"></block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
      </block>
    </statement>
  </block>
</xml>