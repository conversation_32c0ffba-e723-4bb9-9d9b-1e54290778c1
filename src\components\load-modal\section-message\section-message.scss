@use 'components/shared/styles/mixins' as *;

.load-strategy__section_message {
    --bg-section-message-warning: rgb(255 156 19 / 8%);

    margin-top: -16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 16px;
    background-color: var(--bg-section-message-warning);

    .icon {
        margin: 0.5rem 0.8rem 0 0;

        svg {
            g {
                path {
                    fill: #e18d00; // until <LegacyInfo1pxIcon in quill icons will be updated with ability to define the <path fill color
                }
            }
        }
    }

    .text {
        width: calc(100% - 24px);
    }

    @include mobile-or-tablet-screen {
        margin-top: -8px;
        margin-bottom: 8px;
        margin-inline: -1.6rem;
        border-radius: 0;
    }
}

.load-strategy__section_message + .load-strategy__local-dropzone-area {
    height: calc(100% - 68px);

    @include mobile-or-tablet-screen {
        height: calc(100% - 76px);
    }
}
