<xml xmlns="http://www.w3.org/1999/xhtml" is_dbot="true" collection="false">
  <variables>
    <variable type="" id="j}8O`Vs+RJljIwPu-_:_" islocal="false" iscloud="false">Stake</variable>
    <variable type="" id="W4$:ZQCmEz#8+=4ysv5Y" islocal="false" iscloud="false">Loss</variable>
    <variable type="" id="mXtFswo{p,|%W1:V-$+r" islocal="false" iscloud="false">Target Profit</variable>
    <variable type="" id="%L?;380E6Lr^3b.%}t5Q" islocal="false" iscloud="false">stake 2</variable>
    <variable type="" id="zL;/OjMKj]BChCZd/$yV" islocal="false" iscloud="false">text</variable>
    <variable type="" id=")*lE27zuuxcP`|b?Z3-w" islocal="false" iscloud="false">text1</variable>
    <variable type="" id="HPYUFQfXP0PmO!7^2?4w" islocal="false" iscloud="false">text2</variable>
    <variable type="" id="8~hklr`eR(wer*wT~4EP" islocal="false" iscloud="false">text3</variable>
  </variables>
  <block type="trade_definition" id="U?~B$PP}][t#^hkxML)C" deletable="false" x="0" y="0">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="fACY`gFZeC4Z97O4Stg+" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">R_100</field>
        <next>
          <block type="trade_definition_tradetype" id="y[%bgFKE)_p#c})yUv83" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="uea^~k,v,k:6|fW3)S=x" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="cL1hYc/!OCI{Srh@`T_2" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="%yq3`s!fB3=`#U}CgK;$" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="sq9F``X][/MHZMhoGL_a" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="text_print" id="<EMAIL>@.~dFL">
        <value name="TEXT">
          <shadow type="text" id="7~+.iCJ}sBq8K!0S6kJ;">
            <field name="TEXT">Marketkiller Bot 💲💲</field>
          </shadow>
        </value>
        <next>
          <block type="variables_set" id="8T-^chXjv284_bA+S#!c">
            <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y" variabletype="">Loss</field>
            <value name="VALUE">
              <block type="math_number" id="FdPnLSpL/,1$gptl..L~">
                <field name="NUM">1000</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="qe}1t..90%AUN+,VbdFU">
                <field name="VAR" id="mXtFswo{p,|%W1:V-$+r" variabletype="">Target Profit</field>
                <value name="VALUE">
                  <block type="math_number" id="QbQhM[UTxomDt}1^n_lj">
                    <field name="NUM">607</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id=")nE6wrr{My/s;@$)%plE">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_" variabletype="">Stake</field>
                    <value name="VALUE">
                      <block type="math_number" id="bJd-eD8~KKE@GhV7~Uy8">
                        <field name="NUM">2</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="yy_xzBHToKl57hixZ5m}">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q" variabletype="">stake 2</field>
                        <value name="VALUE">
                          <block type="math_number" id="ie0F?c(|JU5YDf^K*t;T">
                            <field name="NUM">2</field>
                          </block>
                        </value>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="mh_]o7lP$G:%2?67oeEZ">
        <mutation has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="math_number" id="FwhFR2N~3;}yc;Z}Cz_=">
            <field name="NUM">1</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="XaWP4r.5=7^yyLNS]^{)">
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_" variabletype="">Stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number" id="~Nc?xMi)Vp9!]z/%]T-,">
            <field name="NUM">5</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="during_purchase" id="xv{Cw!GRyq~^nQ|-sfKw" collapsed="true" x="665" y="0"></block>
  <block type="after_purchase" id="j4S(]G$WdBvV5q(]TFUj" x="665" y="96">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="WZ|Ug$4H!)Xs7Y0,z3i8">
        <mutation else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="KLMh`/*nBZ#o[bS`USgq">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_join" id="(($63CU7K+!VoGyZc(x0">
            <field name="VARIABLE" id="zL;/OjMKj]BChCZd/$yV" variabletype="">text</field>
            <statement name="STACK">
              <block type="text_statement" id=":hSZN80m8sLBON5Pwmlq">
                <value name="TEXT">
                  <shadow type="text" id=")l1eRhY9v0?NSI}|Jjs[">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="ZjlQwZP^dn)]?~%GYOMF">
                    <field name="TEXT">Ganhou: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="/{8q)*#;Sdx$rV01}E76">
                    <value name="TEXT">
                      <shadow type="text" id="gE~,Yj}hQpo#4+Qhrj+$">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="read_details" id="%KipeZS,d}ye($KN5{]Y">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="SM^aa2/x7Cx+yd$f]n?.">
                <field name="NOTIFICATION_TYPE">success</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="WT:-E;99(148a/5jL+_n">
                    <field name="VAR" id="zL;/OjMKj]BChCZd/$yV" variabletype="">text</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="15*?kuVX^ziNZV^yCX;m">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_" variabletype="">Stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="WV(V7SU3^tUya|F@s-%@">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q" variabletype="">stake 2</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="text_join" id="hHuUotV?!;/uFX2o`nh.">
            <field name="VARIABLE" id=")*lE27zuuxcP`|b?Z3-w" variabletype="">text1</field>
            <statement name="STACK">
              <block type="text_statement" id="1iv:05*R!AAnZ{m_[c3)">
                <value name="TEXT">
                  <shadow type="text" id="(JHYn(z!sg^MbM0J0WoN">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="sjlnuy;$K^.`zMgp_bOg">
                    <field name="TEXT">Perdeu: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="N)8s!K]HxQ4Juw`?YC(l">
                    <value name="TEXT">
                      <shadow type="text" id="46T3=ZUmw5I7_g$)]DGr">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="math_single" id="RuIu`(9uTtkbN}C.2cvK">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id="l2=[wsrMVa$b(gTqK94m">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id="(6uyT9X`s`@Q5gMU:L,Q">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="dKCL}Rjr2Tg/@9*}QnP,">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="x[L.I*/_y;9+Z8W}QMs)">
                    <field name="VAR" id=")*lE27zuuxcP`|b?Z3-w" variabletype="">text1</field>
                  </block>
                </value>
                <next>
                  <block type="math_change" id="@=GvK`G}pBN7^|W]sSaT">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_" variabletype="">Stake</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="=OQKP}]l:I*vy#]Gbl:m">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_arithmetic" id=",iwB7IM0Bk`/Cs~tL9Uf">
                        <field name="OP">MULTIPLY</field>
                        <value name="A">
                          <shadow type="math_number" id="KnQ*4O:AhEfr2W%aMnJK">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_single" id="33#MkCE:!_@}l4B`t,kq">
                            <field name="OP">ABS</field>
                            <value name="NUM">
                              <shadow type="math_number" id="5V[pXDc+AJ1suT3F$`YJ">
                                <field name="NUM">9</field>
                              </shadow>
                              <block type="read_details" id="FkrJXDmkEac`=N-.^oy^">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <shadow type="math_number" id="KKkYLigH!=~#ak(nLeEK">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_number" id="E?-X*-O@S#EP%IV/%DbI">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id=":nO*y?3I!F`:JEemp4zV">
                        <value name="IF0">
                          <block type="logic_compare" id="JvbBmNkX@-[GvO}zuWi?">
                            <field name="OP">GTE</field>
                            <value name="A">
                              <block type="math_single" id="ur^Y=6p6byE^OCK@j~Pz">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id=",SIr]Le%s#-~A*Z4#-Qz">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="read_details" id="vuhj=N*wOVBeN9uktm;u">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="q#LoV!UiLz@Pg7Ss88)]">
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y" variabletype="">Loss</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="$}|~`~]ghhoVtWakCd.v">
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_" variabletype="">Stake</field>
                            <value name="VALUE">
                              <block type="variables_get" id="`1MIT|N%P+3,szNr`Vs^">
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q" variabletype="">stake 2</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="text_join" id="AtvsSAj7z[k$;,x7?rte">
            <field name="VARIABLE" id="HPYUFQfXP0PmO!7^2?4w" variabletype="">text2</field>
            <statement name="STACK">
              <block type="text_statement" id="v39MVS7v%g#:sym;`(+!">
                <value name="TEXT">
                  <shadow type="text" id="fx47)P6r,%Z(B*@!6nTA">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="phYQO|7B}Kt?5Ur|p9;T">
                    <field name="TEXT">Total Profit: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="g:kS;qh-V/(~X[/)0{L7">
                    <value name="TEXT">
                      <shadow type="text" id=")6zCd!!/FFwiHqIh9p%3">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="total_profit" id="8uGL9GX#Jff^ELX/+wle"></block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="wNju{9])H$o)Z@~GpZZw">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id=":sWnn_$qOfQ5cd%zd:3f">
                    <field name="VAR" id="HPYUFQfXP0PmO!7^2?4w" variabletype="">text2</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="./ts)/{~;=fd|@m$1dqY">
                    <mutation else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="P-D:F45w-Vu-56-akm8S">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id="#xQ.Y0NFDrA:nCO?s+T?"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="~=u[P.d`bdArUzcK!_46">
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r" variabletype="">Target Profit</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="trade_again" id="_lIDYS$x$nfW$UYBPZ#Z"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="r9gN2lQ;Cb(dl=$Uxab:">
                        <field name="VARIABLE" id="8~hklr`eR(wer*wT~4EP" variabletype="">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="{b$1RkInSaq8:C0!kg%^">
                            <value name="TEXT">
                              <shadow type="text" id="Fx6,N^qgRqGUBy#YP[3b">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="DOb{mzjdZIZwSayGDrq$">
                                <field name="TEXT">Dollars Printed Successfully : </field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="n]QwYYA|n8jE(6Tim+ln">
                                <value name="TEXT">
                                  <shadow type="text" id="4hf,5f(5`1+{;Y|@Q]#_">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="{E2H3/D@57CHe-4hE7Da"></block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id="rxU!duI4V`2C%n4+YoX{">
                            <value name="TEXT">
                              <shadow type="text" id="Qr4pEOpGG(DDoBSYNSr8">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="m%Ml=e~8[C9!ZFYm]+rQ">
                                <field name="VAR" id="8~hklr`eR(wer*wT~4EP" variabletype="">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="WuewGPIIbyL_Ci=}35`Z" collapsed="true" deletable="false" x="0" y="792">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="purchase" id="hLY9BTxCP|;f4Uf/p|NH">
        <field name="PURCHASE_LIST">DIGITOVER</field>
      </block>
    </statement>
  </block>
  <block type="math_number" id="oR,`Dr0[wbUB{0Jd#44+" disabled="true" x="0" y="1760">
    <field name="NUM">5</field>
  </block>
  <block type="text" id="b#fyLk/Rat]BerxcwFAy" collapsed="true" disabled="true" x="0" y="1848">
    <field name="TEXT">Expert  Speed Bot</field>
  </block>
</xml>