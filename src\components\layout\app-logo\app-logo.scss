.app-header {
    &__logo-container {
        display: flex;
        align-items: center;
        padding-inline-start: 1.2rem;
        padding-inline-end: 2rem;
        border-inline-end: 1px solid var(--general-section-1);
        gap: 1rem;

        // Responsive adjustments for mobile and tablet
        @media (max-width: 768px) {
            padding-inline-start: 0.8rem;
            padding-inline-end: 1rem;
            gap: 0.5rem;
            justify-content: flex-start;
        }
    }

    &__logo {
        height: 32px;
        display: flex;
        align-items: center;
    }

    &__logo-icons {
        display: flex;
        align-items: center;
        gap: 0.8rem;

        // Responsive adjustments for mobile and tablet
        @media (max-width: 768px) {
            gap: 0.4rem;
        }
    }

    &__logo-icon-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 0.4rem;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        transition: background-color 0.2s ease;

        // Responsive adjustments for mobile and tablet
        @media (max-width: 768px) {
            width: 36px;
            height: 36px;
            padding: 0.3rem;

            img {
                width: 28px !important;
                height: 28px !important;
            }
        }

        // Extra small mobile devices
        @media (max-width: 480px) {
            width: 32px;
            height: 32px;
            padding: 0.2rem;

            img {
                width: 24px !important;
                height: 24px !important;
            }
        }

        &:hover {
            background-color: var(--general-hover);
        }

        &:active {
            background-color: var(--general-active);
        }

        svg, img {
            transition: opacity 0.2s ease;
        }

        &:hover svg, &:hover img {
            opacity: 0.8;
        }
    }

    &__menu-icon-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        transition: background-color 0.2s ease;
        margin-right: 0.5rem;

        &:hover {
            background-color: var(--general-hover);
        }

        &:active {
            background-color: var(--general-active);
        }

        svg {
            transition: opacity 0.2s ease;
        }

        &:hover svg {
            opacity: 0.8;
        }

        // Responsive adjustments for mobile and tablet
        @media (max-width: 768px) {
            width: 40px;
            height: 40px;
            padding: 0.4rem;
            margin-right: 0.3rem;
        }

        // Extra small mobile devices
        @media (max-width: 480px) {
            width: 36px;
            height: 36px;
            padding: 0.3rem;
            margin-right: 0.2rem;
        }
    }
}
