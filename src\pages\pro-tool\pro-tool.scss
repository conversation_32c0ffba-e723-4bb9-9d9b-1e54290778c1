// PRO TRADER CSS - Exact styling for the original design

.pro-trader {
  min-height: 100vh;
  width: 100%;
  padding: 2rem;
  background: #ffffff;

  h1 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: #111827;
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @media (min-width: 640px) {
      font-size: 3rem;
    }
  }

  .trade-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2.5rem;
    max-width: 90rem;
    margin: 0 auto;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 3rem;
    }

    @media (min-width: 1280px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 3.5rem;
    }
  }
  
  .trade-card {
    width: 100%;
    min-height: 500px;
    border-radius: 1.5rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    background: #ffffff;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
      position: relative;
      padding: 1.5rem 2rem;
      color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      &.digits {
        background: linear-gradient(90deg, #3b82f6, #0ea5e9, #06b6d4);
      }
      
      &.evenodd {
        background: linear-gradient(90deg, #d946ef, #a855f7, #6366f1);
      }
      
      &.risefall {
        background: linear-gradient(90deg, #10b981, #22c55e, #84cc16);
      }
      
      &.matchdiff {
        background: linear-gradient(90deg, #f43f5e, #ef4444, #f97316);
      }
      
      .header-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        
        .icon-wrapper {
          padding: 0.5rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 0.75rem;
        }
        
        h3 {
          font-size: 1rem;
          font-weight: 600;
          letter-spacing: 0.025em;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          margin: 0;
          
          @media (min-width: 640px) {
            font-size: 1.125rem;
          }
        }
      }
      
      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.375rem 0.625rem;
        border-radius: 9999px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        
        &.active {
          background: rgba(255, 255, 255, 0.9);
          color: #047857;
        }
        
        &.inactive {
          background: rgba(0, 0, 0, 0.2);
          color: white;
        }
        
        .status-dot {
          height: 0.625rem;
          width: 0.625rem;
          border-radius: 50%;
          
          &.active {
            background-color: #10b981;
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }
          
          &.inactive {
            background-color: #f43f5e;
          }
        }
      }
    }
    
    .card-content {
      padding: 2rem;
      
      .form-section {
        margin-bottom: 1.25rem;
        
        .form-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 0.75rem;
          
          @media (min-width: 640px) {
            grid-template-columns: repeat(3, 1fr);
          }
        }
        
        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #4b5563;
          }
          
          input, select {
            width: 100%;
            border-radius: 0.5rem;
            border: 1px solid #d1d5db;
            padding: 0.5rem;
            font-size: 0.875rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
            
            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }
        
        .radio-group {
          display: flex;
          align-items: center;
          gap: 1rem;
          
          label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            cursor: pointer;
            
            input[type="radio"] {
              width: auto;
              margin: 0;
            }
          }
        }
      }
      
      .controls-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        
        .control-label {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          
          span {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
          }
        }
        
        .control-buttons {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          
          button {
            border-radius: 0.75rem;
            padding: 0.5rem 1rem;
            font-weight: 700;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            font-size: 0.875rem;
            
            &.btn-primary {
              background: #111827;
              color: white;
              
              &:hover {
                background: rgba(0, 0, 0, 0.8);
              }
            }
            
            &.btn-danger {
              background: #dc2626;
              color: white;
              
              &:hover {
                background: #b91c1c;
              }
            }
          }
        }
      }
      
      .status-footer {
        text-align: center;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        background: #f9fafb;
        border-radius: 0.75rem;
        padding: 0.75rem;
        border: 1px solid #e5e7eb;
      }
    }
  }
}

// Switch component styling
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;

    &:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }
  }

  input:checked + .slider {
    background-color: #2196F3;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked + .slider:before {
    transform: translateX(26px);
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
